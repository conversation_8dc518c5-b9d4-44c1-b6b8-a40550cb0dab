import clsx from 'clsx';
import { Download } from 'lucide-react';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { NotificationContainer } from '@/components/ui';
import AgentsDropdown from '@/components/ui/AgentsDropdown';
import { Button } from '@/components/ui/Button';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { useNotifications } from '@/hooks/useNotifications';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import {
  useAgentPromptsApi,
  useAIAgentsApi,
  usePromptActivityLogApi,
} from '@/services/upivotalAgenticService';
import {
  AgentPrompt,
  AIAgent,
  PromptActivityModification,
} from '@/types/agents';
import { UserBasicInfoPayload } from '@/types/user';

import AgentInfo from './AgentInfo';
import AgentPrompts from './AgentPrompts';

type TabType = 'Agent Info' | 'Agent Prompts';

const AgentDetails = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const locationState = location.state as {
    selectedAgent?: AIAgent;
  } | null;

  const { agentId } = useParams<{ agentId: string }>();
  const { setActiveAgent } = useTenant();
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();

  // Get all agents from all suites
  const allAgents =
    userData?.userInfo?.tenant?.claimedAgentSuites?.flatMap(suite =>
      suite.suite.availableAgents.map(agent => ({
        ...agent,
        suiteKey: suite.suite.agentSuiteKey,
      }))
    ) || [];

  const [activeTab, setActiveTab] = useState<TabType>('Agent Info');
  const tabs: TabType[] = ['Agent Info', 'Agent Prompts'];

  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  const [selectedAgent, setSelectedAgent] = useState<string>(agentId!);
  const [currentAgent, setCurrentAgent] = useState<AIAgent | null>(null);
  const [isAgentDropdownOpen, setIsAgentDropdownOpen] = useState(false);
  const agentDropdownRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(agentDropdownRef, () => setIsAgentDropdownOpen(false));

  // Prompt management state
  const [agentPrompts, setAgentPrompts] = useState<AgentPrompt[]>([]);
  const [promptActivityLog, setPromptActivityLog] = useState<
    PromptActivityModification[]
  >([]);
  const [isLoadingPrompts, setIsLoadingPrompts] = useState(false);
  const [isLoadingActivityLog, setIsLoadingActivityLog] = useState(false);
  const [isLoadingAgent, setIsLoadingAgent] = useState(false);
  const [agentNotFound, setAgentNotFound] = useState(false);

  // Activity log detail view state
  const [selectedActivity, setSelectedActivity] = useState<{
    activity: PromptActivityModification;
    agentName: string;
  } | null>(null);

  // API hooks
  const { getAgentPrompts } = useAgentPromptsApi();
  const { getPromptActivityLog } = usePromptActivityLogApi();
  const getAIAgents = useAIAgentsApi();

  const { notify, notifications, dismiss } = useNotifications();

  // Function to fetch agent data from public API
  const fetchAgentData = async (agentKey: string) => {
    setIsLoadingAgent(true);
    setAgentNotFound(false);
    try {
      const response = await getAIAgents();
      if (response.status && response.data.aiAgents) {
        const foundAgent = response.data.aiAgents.find(
          agent => agent.agentKey === agentKey
        );
        if (foundAgent) {
          setCurrentAgent(foundAgent);
          return foundAgent;
        }
      }
      setAgentNotFound(true);
      return null;
    } catch (error) {
      console.error('Failed to fetch agent data:', error);
      setAgentNotFound(true);
      return null;
    } finally {
      setIsLoadingAgent(false);
    }
  };

  // Function to fetch prompt data for the current agent
  const fetchPromptData = async (agentKey: string) => {
    try {
      // Fetch prompts and activity log in parallel
      setIsLoadingPrompts(true);
      setIsLoadingActivityLog(true);

      const [promptsResponse, activityLogResponse] = await Promise.all([
        getAgentPrompts(agentKey),
        getPromptActivityLog(agentKey),
      ]);

      if (promptsResponse.status) {
        setAgentPrompts(promptsResponse.data);
      }

      if (activityLogResponse.status) {
        setPromptActivityLog(activityLogResponse.data.modifications);
      }
    } catch (error) {
      console.error('Failed to fetch prompt data:', error);
    } finally {
      setIsLoadingPrompts(false);
      setIsLoadingActivityLog(false);
    }
  };

  useEffect(() => {
    const initializeAgent = async () => {
      if (locationState?.selectedAgent) {
        // Agent data is available from navigation state
        try {
          setCurrentAgent(locationState.selectedAgent);
          await fetchPromptData(locationState.selectedAgent.agentKey);
        } catch (error) {
          console.error('Failed to switch agent:', error);
        }
      } else if (agentId && !currentAgent) {
        // Fallback: fetch agent data from API if not available (page refresh case)
        try {
          const agent = await fetchAgentData(agentId);
          if (agent) {
            await fetchPromptData(agent.agentKey);
          } else {
            console.error('Agent not found:', agentId);
          }
        } catch (error) {
          console.error('Failed to fetch agent data:', error);
        }
      }
    };

    initializeAgent();
  }, [locationState?.selectedAgent, agentId, currentAgent]);

  // Handle agent selection change
  const handleAgentChange = (agentKey: string) => {
    setSelectedAgent(agentKey);
    setActiveAgent(agentKey);
    setIsAgentDropdownOpen(false);
    navigate(ROUTES.AGENT_DETAILS(agentKey));
    fetchAgentData(agentKey);
    fetchPromptData(agentKey);
  };

  // Handle activity log detail view
  const handleViewActivity = (activity: PromptActivityModification) => {
    setSelectedActivity({
      activity,
      agentName: currentAgent?.agentName || 'Unknown Agent',
    });
  };

  const handleBackFromActivityDetail = () => {
    setSelectedActivity(null);
  };

  // Activity log detail download function
  const handleDownloadActivity = () => {
    if (!selectedActivity) return;

    const { activity, agentName } = selectedActivity;
    const activityData = {
      agentName,
      dateModified: activity.dateModified,
      modifiedBy: `${activity.modifiedByFirstName} ${activity.modifiedByLastName}`,
      action: activity.action,
      timestamp: moment(activity.dateModified).format('LLLL'),
    };

    const dataStr = JSON.stringify(activityData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${agentName}_activity_${moment(activity.dateModified).format('YYYY-MM-DD_HH-mm-ss')}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Inline ActivityLogDetail component
  const ActivityLogDetail = () => {
    if (!selectedActivity) return null;

    const { activity, agentName } = selectedActivity;

    return (
      <div className="h-full overflow-y-auto">
        {/* Header */}
        <div className="mb-6 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Activity Log Details
            </h1>
            <p className="text-gray-600">
              Detailed information about the prompt activity
            </p>
          </div>
          <Button onClick={handleBackFromActivityDetail} variant="outline">
            Back to Agent Prompts
          </Button>
        </div>

        {/* Activity Details Card */}
        <div className="rounded-lg bg-white p-6 shadow-sm">
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Activity Information
            </h2>
            <Button onClick={handleDownloadActivity} variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Download Details
            </Button>
          </div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Agent Name
              </label>
              <p className="mt-1 text-sm text-gray-900">{agentName}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Action
              </label>
              <p className="mt-1 text-sm text-gray-900">
                {activity.action.charAt(0).toUpperCase() +
                  activity.action.slice(1).toLowerCase()}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Modified By
              </label>
              <p className="mt-1 text-sm text-gray-900">
                {activity.modifiedByFirstName} {activity.modifiedByLastName}
              </p>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">
                Date & Time
              </label>
              <p className="mt-1 text-sm text-gray-900">
                {moment(activity.dateModified).format('LLLL')}
              </p>
              <p className="mt-1 text-xs text-gray-500">
                ({moment(activity.dateModified).fromNow()})
              </p>
            </div>
          </div>
        </div>

        {/* Timeline Visualization */}
        <div className="mt-8 rounded-lg bg-white p-6 shadow-sm">
          <h3 className="mb-4 text-lg font-semibold text-gray-900">
            Activity Timeline
          </h3>
          <div className="flex items-center">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">
              <span className="text-sm font-medium">1</span>
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-medium text-gray-900">
                Prompt {activity.action.toLowerCase()}
              </p>
              <p className="text-sm text-gray-500">
                by {activity.modifiedByFirstName} {activity.modifiedByLastName}
              </p>
              <p className="text-xs text-gray-400">
                {moment(activity.dateModified).format(
                  'MMM DD, YYYY [at] h:mm A'
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex h-full">
      {/* Chat Interface - LHS */}
      <EnhancedChatSidebar
        reloadChatHistoryRef={reloadChatHistoryRef}
        // externalMessage={locationState?.userMessage}
      />

      {/* Main Content - RHS */}
      <div className="flex w-full max-w-[750px] flex-1 flex-col overflow-y-auto p-8">
        {!selectedActivity && (
          <div>
            <div className="relative w-fit" ref={agentDropdownRef}>
              <AgentsDropdown
                isOpen={isAgentDropdownOpen}
                onToggle={() => setIsAgentDropdownOpen(!isAgentDropdownOpen)}
                currentItem={allAgents
                  .map(a => ({
                    id: a.agentKey,
                    name: a.agentName,
                    icon: a.avatar,
                  }))
                  .find(a => a.id === selectedAgent)}
                options={allAgents.map(a => ({
                  id: a.agentKey,
                  name: a.agentName,
                  icon: a.avatar,
                }))}
                onItemSelect={agent => handleAgentChange(agent.id)}
                placeholder="Agent"
                noOptionsMessage="No other agents available"
              />
            </div>
            {/* Header */}
            <div className="my-6">
              {/* Tabs */}
              <div className="flex space-x-1 border-b border-gray-200">
                {tabs.map(tab => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={clsx(
                      'px-4 py-2 font-spartan text-sm font-medium transition-colors',
                      activeTab === tab
                        ? 'border-b-2 border-primary text-primary'
                        : 'text-gray-600 hover:text-blackOne'
                    )}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Content */}
        <div>
          {isLoadingAgent ? (
            <div className="flex h-64 items-center justify-center">
              <div className="text-center">
                <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                <p className="text-gray-600">Loading agent details...</p>
              </div>
            </div>
          ) : agentNotFound ? (
            <div className="flex h-64 items-center justify-center">
              <div className="text-center">
                <h2 className="mb-2 text-xl font-semibold text-gray-900">
                  Agent Not Found
                </h2>
                <p className="mb-4 text-gray-600">
                  The requested agent could not be found.
                </p>
                <Button onClick={() => window.history.back()} variant="outline">
                  Go Back
                </Button>
              </div>
            </div>
          ) : selectedActivity ? (
            <ActivityLogDetail />
          ) : (
            <>
              {/* Notifications Container */}
              <NotificationContainer
                notifications={notifications}
                onClose={dismiss}
                className="w-full"
                maxNotifications={3}
              />

              {/* Agent Info */}
              {activeTab === 'Agent Info' && currentAgent && (
                <AgentInfo currentAgent={currentAgent} />
              )}

              {/* Agent Prompts */}
              {activeTab === 'Agent Prompts' && currentAgent && (
                <AgentPrompts
                  currentAgent={currentAgent}
                  agentPrompts={agentPrompts}
                  promptActivityLog={promptActivityLog}
                  isLoadingPrompts={isLoadingPrompts}
                  isLoadingActivityLog={isLoadingActivityLog}
                  onPromptUpdate={() =>
                    fetchPromptData(currentAgent?.agentKey || '')
                  }
                  onViewActivity={handleViewActivity}
                />
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentDetails;
