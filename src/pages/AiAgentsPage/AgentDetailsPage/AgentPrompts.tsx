import 'react-quill/dist/quill.snow.css';

// DOCX generation imports
import { Document, Packer, Paragraph, TextRun } from 'docx';
// import parse from 'html-react-parser';
import { Download, PencilLine } from 'lucide-react';
import moment from 'moment';
import React, { useState } from 'react';
import ReactQuill from 'react-quill';

import DoneOrCancelWidget from '@/components/ui/DoneOrCancelWidget';
import { useNotifications } from '@/hooks/useNotifications';
import { useAgentPromptsApi } from '@/services/upivotalAgenticService';
import { AgentPromptsProps } from '@/types/agents';

const AgentPrompts: React.FC<AgentPromptsProps> = ({
  currentAgent,
  agentPrompts,
  promptActivityLog,
  isLoadingPrompts,
  isLoadingActivityLog,
  onPromptUpdate,
  onViewActivity,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [showAllActivities, setShowAllActivities] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // API hooks
  const { updateAgentPrompt } = useAgentPromptsApi();
  const { notify } = useNotifications();

  // Get the active prompt
  const activePrompt = agentPrompts.find(p => p.isActive);

  // Set initial prompt content when agentPrompts change
  React.useEffect(() => {
    if (activePrompt && !isEditing) {
      // Convert plain text content to HTML format for ReactQuill
      const htmlContent = textToHtml(activePrompt.content);
      setPrompt(htmlContent);
    }
  }, [activePrompt, isEditing]);

  const handleUpdatePrompt = async (value: string) => {
    if (!activePrompt) {
      notify('No active prompt found to update');
      return;
    }

    setIsUpdating(true);
    try {
      // Convert HTML content back to plain text for API
      const plainTextContent = htmlToText(value);

      const response = await updateAgentPrompt(activePrompt.id, {
        name: activePrompt.name,
        content: plainTextContent,
        agentKey: activePrompt.agentKey,
      });

      if (response.status) {
        notify(response.message || 'Prompt updated successfully');
        onPromptUpdate(); // Refresh the data
      } else {
        notify(response.message || 'Failed to update prompt');
      }
    } catch (error) {
      notify('Failed to update prompt. Please try again.');
      console.error('Update prompt error:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  // Converts plain text with newlines, **bold**, _italics_ etc. to safe HTML
  const textToHtml = (text: string): string => {
    if (!text) return '';

    // Escape HTML
    let html = text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');

    // Markdown-style formatting
    html = html
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // **bold**
      .replace(/_(.*?)_/g, '<em>$1</em>'); // _italics_

    // Preserve multiple newlines faithfully
    html = html
      // Convert 3+ newlines to 2 <br> (visually 1 blank line)
      .replace(/\n{3,}/g, '<br><br>')
      // Convert single or double newlines to one <br>
      .replace(/\n/g, '<br>');

    // Finally, wrap in <p> so Quill treats it as a proper block
    return `<p>${html}</p>`;
  };

  // Converts Quill HTML content back to plain text
  const htmlToText = (html: string): string => {
    if (!html) return '';

    const htmlWithNewlines = html
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/<\/p>\s*<p>/gi, '\n\n')
      .replace(/<\/?(strong|b)>/gi, '**')
      .replace(/<\/?(em|i)>/gi, '_')
      .replace(/<\/?[^>]+(>|$)/g, '');

    return htmlWithNewlines.replace(/\n{3,}/g, '\n\n').trim();
  };

  const handleDownloadPrompt = async () => {
    if (!activePrompt) {
      notify('No active prompt found to download');
      return;
    }

    try {
      // Convert HTML content to plain text
      const plainTextContent = htmlToText(activePrompt.content);

      // Create a new document
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: `${currentAgent?.agentName} Prompt`,
                    bold: true,
                    size: 32,
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: `Last Updated: ${moment(activePrompt.updatedAt).format('lll')}`,
                    size: 20,
                  }),
                ],
              }),
              new Paragraph({
                children: [new TextRun({ text: '' })], // Empty line
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Prompt Content:',
                    bold: true,
                    size: 24,
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: plainTextContent,
                    size: 20,
                  }),
                ],
              }),
            ],
          },
        ],
      });

      // Generate and download the document
      const blob = await Packer.toBlob(doc);
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${currentAgent?.agentName}_prompt_${moment().format('YYYY-MM-DD')}.docx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      notify('Prompt downloaded successfully');
    } catch (error) {
      notify('Failed to download prompt. Please try again.');
      console.error('Download prompt error:', error);
    }
  };

  return (
    <div className="mt-2">
      <div className="w-full rounded-[10px] border border-grayTwentyEight bg-lightOrangeTwo px-4 py-1.5 capitalize">
        <div className="flex w-full items-center justify-between">
          <div>
            <p className="py-1 text-lg font-medium text-[#0F0006]">
              {currentAgent?.agentName} Prompt Update
            </p>
            <p className="text-xs">
              Last Updated:{' '}
              {activePrompt
                ? moment(activePrompt.updatedAt).format('lll')
                : 'N/A'}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            {!isEditing ? (
              <button
                onClick={e => {
                  e.stopPropagation();
                  setIsEditing(true);
                }}
                className="flex items-center gap-2 rounded-full border border-grayTen px-4 py-2 text-sm transition-colors hover:border-primary hover:bg-red-50 hover:text-primary"
              >
                Edit
                <PencilLine className="h-4 w-4" />
              </button>
            ) : (
              <button
                onClick={async e => {
                  e.stopPropagation();
                  await handleUpdatePrompt(prompt);
                  setIsEditing(false);
                }}
                disabled={isUpdating}
                className="flex items-center gap-2 rounded-full border border-[#3E8E58] bg-[#3E8E58] px-4 py-2 text-sm text-white transition-colors hover:border-[#2ead59] hover:bg-[#2ead59] disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isUpdating && (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                )}
                Publish
                <PencilLine className="h-4 w-4" />
              </button>
            )}

            <button
              onClick={handleDownloadPrompt}
              className="flex items-center gap-2 rounded-full border border-grayTen px-4 py-2 text-sm transition-colors hover:border-primary hover:bg-red-50 hover:text-primary"
            >
              Download
              <Download className="h-4 w-4" />
            </button>
          </div>
        </div>

        <div className="relative mb-10 pb-2 pt-4">
          {isLoadingPrompts ? (
            <div className="flex items-center justify-center p-8">
              <div className="text-gray-500">Loading prompt...</div>
            </div>
          ) : (
            <ReactQuill
              theme="snow"
              value={prompt}
              onChange={setPrompt}
              readOnly={!isEditing}
            />
          )}
          {isEditing && (
            <div className="absolute -bottom-9 right-0 flex gap-x-1">
              <DoneOrCancelWidget
                done={async () => {
                  await handleUpdatePrompt(prompt);
                  setIsEditing(false);
                }}
                cancel={() => setIsEditing(false)}
                isDoneLoading={isUpdating}
              />
            </div>
          )}
        </div>
      </div>

      {/* Prompt Activity Log Section */}
      <div className="mt-4 w-full bg-lightOrangeTwo px-4 py-1.5 capitalize">
        <p className="py-2 text-lg font-medium text-[#0F0006]">Activity Log</p>
        {isLoadingActivityLog ? (
          <div className="flex items-center justify-center p-4">
            <div className="text-gray-500">Loading activity log...</div>
          </div>
        ) : promptActivityLog.length > 0 ? (
          <div className="space-y-3">
            {(showAllActivities
              ? promptActivityLog
              : promptActivityLog.slice(0, 3)
            ).map((activity, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <svg
                    width="32"
                    height="32"
                    viewBox="0 0 32 32"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect
                      x="5.5"
                      y="5.5"
                      width="21"
                      height="21"
                      rx="10.5"
                      fill="white"
                    />
                    <rect
                      x="5.5"
                      y="5.5"
                      width="21"
                      height="21"
                      rx="10.5"
                      stroke="#FF3E00"
                      strokeWidth="11"
                    />
                  </svg>

                  <div>
                    <div className="mb-1.5 text-sm text-gray-400">
                      {moment(activity.dateModified).format('LLL')}
                    </div>
                    <div className="text-sm">{`${activity.modifiedByFirstName ?? ''} ${activity.modifiedByLastName ?? ''} ${activity.action.toLowerCase()} the document`}</div>
                  </div>
                </div>

                <div
                  className="cursor-pointer text-primary underline"
                  onClick={() => onViewActivity?.(activity)}
                >
                  View
                </div>
              </div>
            ))}

            {/* Load More Button */}
            {promptActivityLog.length > 3 && !showAllActivities && (
              <div className="flex justify-center pt-3">
                <button
                  onClick={() => setShowAllActivities(true)}
                  className="rounded-md border border-primary px-4 py-2 text-sm font-medium text-primary transition-colors hover:bg-primary hover:text-white"
                >
                  Load more
                </button>
              </div>
            )}
          </div>
        ) : (
          <p className="text-sm text-gray-400">No activity log available</p>
        )}
      </div>
    </div>
  );
};

export default AgentPrompts;
