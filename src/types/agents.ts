export interface AIAgent {
  agentName: string;
  agentKey: string;
  agentSuiteKey?: string;
  description: string;
  roleDescription: string;
  avatar: string;
  roles: string[];
  categories: string[];
  enabled: boolean;
}

export interface AIAgentsResponse {
  status: boolean;
  message: string;
  data: {
    aiAgents: AIAgent[];
    total: number;
    page: number;
    pageSize: number;
  };
}

export interface AgentCategory {
  categoryName: string;
  categoryAlias: string;
}

export interface AgentCategoriesResponse {
  status: boolean;
  message: string;
  data: AgentCategory[];
}

export interface AIAgentSuite {
  agentSuiteName: string;
  agentSuiteKey: string;
  description: string;
  roleDescription: string;
  avatar: string;
  availableAgents: AIAgent[];
  roles: string[];
  enabled: boolean;
}

export interface AIAgentsSuiteData {
  aiAgentSuites: AIAgentSuite[];
  total: number;
  page: number;
  pageSize: number;
}

export interface AIAgentsSuiteResponse {
  status: boolean;
  message: string;
  data: AIAgentsSuiteData;
}

export type AgentInfoProps = {
  currentAgent: AIAgent;
};

export type AgentPromptsProps = {
  currentAgent: AIAgent;
  agentPrompts: AgentPrompt[];
  promptActivityLog: PromptActivityModification[];
  isLoadingPrompts: boolean;
  isLoadingActivityLog: boolean;
  onPromptUpdate: () => void;
  onViewActivity?: (activity: PromptActivityModification) => void;
};

export type MessageSender = string;

export interface ChatMessage {
  id: string;
  sender: MessageSender;
  content: string;
  timestamp: Date;
  senderName: string;
  senderIcon?: string;
}

export interface ChatState {
  messages: ChatMessage[];
  isLoading: boolean;
  sessionId: string;
}

export interface OnboardingProgress {
  firstName?: string;
  lastName?: string;
  email?: string;
  verificationCode?: string;
  password?: string;
  emailVerified?: boolean;
  isSignupStarted: boolean;
  isSignupCompleted: boolean;
  currentStep: OnboardingStep;
}

export type OnboardingStep =
  | 'initial'
  | 'firstName'
  | 'lastName'
  | 'email'
  | 'verification'
  | 'password'
  | 'completed';

export interface ConversationState {
  messages: ChatMessage[];
  onboardingProgress: OnboardingProgress;
  isLoading: boolean;
  showSignupButton: boolean;
  showProceedToDashboardButton: boolean;
  sessionId: string;
}

export interface PasswordValidation {
  minLength: boolean;
  hasLowercase: boolean;
  hasUppercase: boolean;
  hasDigit: boolean;
  hasSpecialChar: boolean;
}

export interface SidebarState {
  showMarketplaceAgents: boolean;
  showOnboardingFields: boolean;
  onboardingData: OnboardingProgress;
}

// API Types
export interface ChatApiRequest {
  userMessage: string;
}

export interface ChatApiResponse {
  message: string;
}

export interface ChatRequest {
  userMessage: string;
  sessionId: string;
  currentPage?: string;
}

export interface ChatHistoryItem {
  userId: string;
  conversationId: string;
  message: string;
  sender: string;
  createdAt: string;
}

export interface ChatResponse {
  message: string;
}

interface Activity {
  dateModified: string;
  modifiedBy: string;
  modifiedByFirstName: string;
  modifiedByLastName: string;
  action: string;
}

export interface ActivityLogResponse {
  status: boolean;
  message: string;
  data: {
    id: 'string';
    fileRef: 'string';
    modifications: Activity[];
  };
}

// Prompt Management Types
export interface AgentPrompt {
  id: string;
  name: string;
  content: string;
  agentKey: string;
  version: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PromptResponse {
  status: boolean;
  message: string;
  data: AgentPrompt[];
}

export interface UpdatePromptRequest {
  name: string;
  content: string;
  agentKey: string;
}

export interface UpdatePromptResponse {
  status: boolean;
  message: string;
  data: null;
}

export interface PromptActivityModification {
  dateModified: string;
  modifiedBy: string;
  modifiedByFirstName: string;
  modifiedByLastName: string;
  action: 'CREATED' | 'UPDATED';
}

export interface PromptActivityLogResponse {
  status: boolean;
  message: string;
  data: {
    agentKey: string;
    modifications: PromptActivityModification[];
  };
}
