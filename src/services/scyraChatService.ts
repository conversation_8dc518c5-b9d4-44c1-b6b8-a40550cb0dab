import moment from 'moment';
import { useCallback } from 'react';

import {
  usePivotlPrivateRequest,
  useUnauthenticatedAgentRequest,
} from '@/lib/axios/usePrivateRequest';
import { ChatHistoryItem, ChatRequest } from '@/types/agents';
import { agenticService } from '@/utils/apiServiceControllersRoute';
import { BASE_URL } from '@/utils/apiUrls';

import { useAuth } from '../context/AuthContext';
import { useTenant } from '../context/TenantContext';

// Utility function to filter out requestField JSON data from messages
export const filterRequestFieldFromMessage = (message: string): string => {
  if (!message) return message;

  let filteredMessage = message;

  // Remove JSON markdown blocks (new format)
  filteredMessage = filteredMessage
    .replace(/```json\s*\{.*?\}\s*```/gs, '')
    .trim();

  // Remove plain JSON from display (old format - for backward compatibility)
  filteredMessage = filteredMessage
    .replace(/\{\s*"requestField"\s*:\s*"[^"]+"\s*\}\s*/g, '')
    .trim();

  // Additional cleanup for any remaining JSON-like structures
  filteredMessage = filteredMessage.replace(/\s+/g, ' ').trim();

  return filteredMessage;
};

export const CHAT_ENDPOINT = `${agenticService}/ai-agents/chat`;
export const HISTORY_ENDPOINT = `${CHAT_ENDPOINT}/history`;

type ApiResponse<T> = Promise<T>;

const useScyraApi = () => {
  const { tenantId, activeAgent, isAgentSwitching, waitForAgentReady } =
    useTenant();
  const { isAuthenticated } = useAuth();
  const agentKey = activeAgent || '';

  // Always call both hooks to maintain consistent hook order
  const authenticatedRequest = usePivotlPrivateRequest(
    BASE_URL,
    tenantId || '',
    agentKey
  );
  const unauthenticatedRequest = useUnauthenticatedAgentRequest(
    BASE_URL,
    agentKey
  );

  // Return the appropriate request based on authentication status
  const axiosInstanceRef = isAuthenticated
    ? authenticatedRequest
    : unauthenticatedRequest;

  return {
    axiosInstanceRef,
    activeAgent,
    isAgentSwitching,
    waitForAgentReady,
  };
};

// Scyra chat hook
export const useScyraChatApi = () => {
  const { axiosInstanceRef, activeAgent, isAgentSwitching, waitForAgentReady } =
    useScyraApi();

  const chatWithScyra = useCallback(
    async (payload: ChatRequest): ApiResponse<string> => {
      try {
        // Wait for agent to be ready if switching
        if (isAgentSwitching) {
          const isReady = await waitForAgentReady();
          if (!isReady) {
            throw new Error('Agent switch timeout - chat request cancelled');
          }
        }

        // Get fresh axios instance AFTER agent is ready
        const axiosInstance = axiosInstanceRef.current;
        if (!axiosInstance) {
          throw new Error(
            'Chat service not ready. Please wait a moment and try again.'
          );
        }

        // Validate agent context with fresh data
        const currentAgent = activeAgent || 'unknown';
        const instanceAgent =
          axiosInstance.defaults?.headers?.['X-Active-Agent'];

        // Double-check agent consistency
        if (instanceAgent && instanceAgent !== currentAgent) {
          console.warn(
            `Agent mismatch detected: context=${currentAgent}, instance=${instanceAgent}. Retrying...`
          );
          // Wait a bit more for consistency
          await new Promise(resolve => setTimeout(resolve, 50));
          const freshInstance = axiosInstanceRef.current;
          if (
            freshInstance?.defaults?.headers?.['X-Active-Agent'] !==
            currentAgent
          ) {
            throw new Error(
              `Agent context mismatch - expected ${currentAgent}`
            );
          }
        }

        // console.log(`Sending chat request to agent: ${currentAgent}`);

        const { data } = await axiosInstance.post(CHAT_ENDPOINT, payload);
        // Filter out requestField JSON data from the response
        const filteredResponse = filterRequestFieldFromMessage(data || '');
        return filteredResponse;
      } catch (error) {
        // Try to surface agent name if present on the axios instance headers or fallback
        const agentName =
          axiosInstanceRef.current?.defaults?.headers?.['X-Active-Agent'] ||
          activeAgent ||
          'agent';
        console.error(`${agentName} Chat API Error:`, error);
        throw new Error(
          `Failed to communicate with ${agentName}. Please try again.`
        );
      }
    },
    [axiosInstanceRef, activeAgent, isAgentSwitching, waitForAgentReady]
  );

  return chatWithScyra;
};

// Chat history hook - always uses authenticated requests
export const useScyraChatHistoryApi = () => {
  const { tenantId, activeAgent, isAgentSwitching, waitForAgentReady } =
    useTenant();
  const agentKey = activeAgent || '';

  // Always use authenticated request for chat history since it's user-specific data
  const authenticatedRequest = usePivotlPrivateRequest(
    BASE_URL,
    tenantId || '',
    agentKey
  );

  const fetchChatHistory = useCallback(async (): ApiResponse<
    ChatHistoryItem[]
  > => {
    try {
      // Wait for agent to be ready if switching
      if (isAgentSwitching) {
        const isReady = await waitForAgentReady();
        if (!isReady) {
          console.warn('Agent switch timeout - returning empty chat history');
          return [];
        }
      }

      const axiosInstance = authenticatedRequest.current;
      if (!axiosInstance) {
        // Return empty array instead of throwing error to prevent chat failures
        console.warn(
          'Axios instance not initialized yet, returning empty chat history'
        );
        return [];
      }

      // Ensure we have the Authorization header before making the request
      const authHeader = axiosInstance.defaults?.headers?.['Authorization'];
      if (!authHeader) {
        console.warn(
          'No Authorization header found, waiting for authentication...'
        );
        return [];
      }

      const { data } = await axiosInstance.get(HISTORY_ENDPOINT);

      // Normalize messages
      const normalizedHistory: ChatHistoryItem[] = (data || []).map(
        (item: ChatHistoryItem) => {
          const localDate = moment.utc(item.createdAt).local().toDate(); // Convert to local timezone
          return {
            ...item,
            message: filterRequestFieldFromMessage(item.message),
            createdAt: localDate,
          };
        }
      );

      return normalizedHistory;
    } catch (error) {
      console.error('Failed to fetch chat history:', error);
      // Return empty array instead of throwing to prevent chat failures
      return [];
    }
  }, [authenticatedRequest, activeAgent, isAgentSwitching, waitForAgentReady]);

  return fetchChatHistory;
};
