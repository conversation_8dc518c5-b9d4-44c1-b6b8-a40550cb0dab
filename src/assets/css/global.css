@import "./fonts.css";
@import "./variables.css";
@import "./loader.css";
@import "./reactTelInput.css";
@import "./reactCalender.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply m-0 box-border scroll-smooth p-0 font-spartan;
  }
  select,
  input,
  .input {
    box-shadow: none !important;
  }
}
@layer components {
  .input {
    @apply h-[37px] min-h-[37px] w-full max-w-[457] rounded-md border 
    border-grayFifteen bg-white px-[26px] text-[13px] placeholder-gray-400 outline-[0] transition duration-500 ease-in-out placeholder:text-[13px] focus:border-primary focus:ring-0;
  }
  .inputPin {
    @apply h-[42px] min-h-[42px] w-full max-w-[60px] border border-grayFifteen bg-white 
    text-[14px] outline-[0] transition duration-500 ease-in-out hover:border-primary focus:border-primary focus:ring-0;
  }

  #tooltip[data-popper-placement^="top"] > #arrow {
    @apply -bottom-1;
  }

  #tooltip[data-popper-placement^="bottom"] > #arrow {
    @apply -top-1;
  }

  #tooltip[data-popper-placement^="left"] > #arrow {
    @apply -right-1;
  }

  #tooltip[data-popper-placement^="right"] > #arrow {
    @apply -left-1;
  }

  #arrow,
  #arrow::before {
    @apply absolute z-[-1] h-3 w-3;
  }

  #arrow::before {
    @apply block rotate-45 bg-primary; /* Note: Custom rotation and colors may require additional configuration */
    content: "";
  }
  .container_max_width {
    @apply mx-auto w-full xxl:max-w-[1440px];
  }
  .mention {
    padding: 3px 3px 2px;
    margin: "0 1px";
    vertical-align: baseline;
    display: inline-block;
    border-radius: 4px;
    background-color: #eee;
    color: hsl(var(--primary));
    font-size: 0.9em;
    font-weight: 700;
  }
}

@layer utilities {
  .section-x-padding {
    @apply px-5 sm:px-16 md:px-28;
  }
  .section-x-padding-alt {
    @apply px-5 lg:px-[55px];
  }
  .listStyle ul {
    @apply list-inside list-disc;
  }

  .gradient-text {
    background: linear-gradient(
      100.06deg,
      var(--light-orange) 20.11%,
      var(--dark-orange) 86.14%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .gradient-bg {
    background: linear-gradient(
      100.06deg,
      var(--light-orange) 20.11%,
      var(--dark-orange) 86.14%
    );
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .\[animation-play-state\:paused\] {
    animation-play-state: paused !important;
  }

  .primary__gradient {
    background: hsl(var(--primary));
    filter: blur(200px);
  }
}

audio,
canvas,
progress,
video {
  vertical-align: baseline;
  display: inline-block;
}

video {
  overflow-clip-margin: content-box;
  overflow: clip;
}

* {
  @apply scrollbar-thin scrollbar-track-transparent scrollbar-thumb-primary  scrollbar-thumb-rounded-md;
}

.user-agreement h2 {
  @apply mb-6 text-[18px] font-semibold xs:text-[20px];
}

.user-agreement p,
.user-agreement li,
.user-agreement h3 {
  @apply mb-2 text-[15px] font-normal xs:text-[16px];
}

.user-agreement ul {
  @apply mb-6 ml-4;
  list-style: disc;
  list-style-position: inside;
}

.user-agreement section {
  @apply mb-6 overflow-x-auto;
}

.benefactor-form .react-tel-input {
  @apply !h-12 !max-w-full;
}

.benefactor-form .benefactor-select__placeholder {
  @apply !text-gray-400;
}

.benefactor-form .input,
.benefactor-form .select [class*="-control"] {
  @apply h-14;
}

.benefactor-form .react-tel-input .form-control {
  @apply !border border-grayFifteen;
}

.bg-gradient-white-gray,
.benefactor-form .react-tel-input .form-control,
.benefactor-form .select [class*="-control"] {
  background:
    linear-gradient(0deg, #fbfafa, #fbfafa),
    linear-gradient(0deg, #bab9b9, #bab9b9) !important;
}

.Termstext,
.Text-terms {
  @apply !hidden;
}

.typing {
  width: 0;
  overflow: hidden;
  white-space: nowrap;
  /* border-right: 2px solid #666; cursor */
  animation:
    typing 4s steps(40, end) forwards,
    blink 0.75s step-end 8;
  /* typing runs once, cursor blinks a few times */
}

/* typing effect */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

/* blinking cursor */
@keyframes blink {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: #666;
  }
}

/* infinite scroll right animation */
@keyframes scroll-right {
  0% {
    transform: translateX(-33.333%);
  }
  100% {
    transform: translateX(0);
  }
}

.app-container {
  @apply mx-auto w-[90%] max-w-[1300px];
}

.ql-container {
  min-height: 10vh;
  max-height: 40vh;
}

.ql-editor {
  min-height: 10vh;
  max-height: 40vh;
  overflow-y: auto;
}
